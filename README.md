# 元梦之星农场自动化脚本

## 项目简介
这是一个专为元梦之星农场设计的自动化脚本，使用ADB命令模拟WASD按键操作，实现精确的角色移动控制。支持PC端调试和手机端直接执行两种模式。

## 核心特性
- ✅ 使用 `adb shell input keyevent --longpress` 实现稳定的按键控制
- ✅ 详细的日志记录和错误诊断
- ✅ 移动距离校准系统
- ✅ 复杂移动序列执行
- ✅ 实时ADB连接监控
- ✅ 支持移动、点击、滑动的混合操作
- ✅ 配置文件管理，支持多个预设序列
- ✅ 手机端直接执行，无需PC连接

## 环境要求

### PC端环境
1. **ADB工具**: 确保ADB已安装并添加到系统PATH
2. **Python 3.6+**: 运行脚本需要Python环境
3. **Android设备**: 开启USB调试模式并连接到电脑

### 手机端环境
1. **Android设备**: Android 4.0+
2. **Root权限**: 需要root权限才能执行shell脚本
3. **终端应用**: 推荐使用 Termux 或其他终端应用

## 快速开始

### PC端使用

#### 1. 检查ADB连接
```bash
adb devices
```
确保显示你的设备状态为 `device`

#### 2. 运行主脚本
```bash
python move_debugger.py
```

#### 3. 功能说明

**主要功能菜单：**
1. **移动测试** - 测试单个方向的移动
2. **移动距离校准** - 确定按键次数与移动距离的关系
3. **统一命令执行** - 融合移动/点击/滑动的混合操作 ⭐
4. **单次按键测试** - 验证按键是否有效
5. **ADB连接状态** - 检查设备连接
6. **屏幕信息** - 获取设备屏幕参数

### 手机端使用

#### 1. 部署脚本文件
将 `auto_game.sh` 和 `config_commands.txt` 复制到手机存储中

#### 2. 设置执行权限
```bash
chmod +x auto_game.sh
```

#### 3. 基本使用
```bash
# 查看配置
sh auto_game.sh -c

# 执行所有命令行（默认行为）
sh auto_game.sh

# 执行指定行命令
sh auto_game.sh 1

# 显示帮助
sh auto_game.sh -h
```

## PC端使用流程

### 第一步：验证按键有效性
选择菜单 `5. 单次按键测试`，确认每个方向键都能让角色移动。

### 第二步：校准移动距离
选择菜单 `2. 移动距离校准`：
- 选择要校准的方向（W/A/S/D）
- 系统会测试按1次、2次、3次、5次、10次的移动效果
- 记录每次测试的移动距离
- 校准数据会保存到 `movement_calibration.txt`

### 第三步：执行统一命令
选择菜单 `3. 统一命令执行`：
- 支持移动、点击、滑动的混合操作
- 移动命令：`W3 A2 S1 D4`
- 点击命令：`540,960` (x,y坐标)
- 滑动命令：`SWIPE:800,500,800,300,500` (起点x,y,终点x,y,持续时间ms)
- 混合命令：`W3 540,960 A2 SWIPE:800,500,800,300,500`

## 手机端使用流程

### 方法一: 使用Termux (推荐)

1. **安装Termux**
   - 从Google Play或F-Droid下载安装Termux

2. **获取Root权限**
   ```bash
   # 在Termux中安装tsu (需要设备已root)
   pkg install tsu
   ```

3. **上传脚本文件**
   - 将脚本文件复制到手机存储
   - 可以通过USB、云盘、或直接在Termux中创建

4. **设置执行权限**
   ```bash
   chmod +x auto_game.sh
   ```

### 方法二: 直接在设备上创建

1. **打开终端应用**
2. **切换到root用户**
   ```bash
   su
   ```
3. **创建脚本文件**
   ```bash
   # 复制脚本内容到文件中
   cat > auto_game.sh << 'EOF'
   # (粘贴脚本内容)
   EOF
   ```

## 统一命令语法

### 移动命令
```
方向键 + 次数
```
- `W5` - 向上移动5次
- `A3 D2` - 向左3次，然后向右2次
- 方向键：W(上) A(左) S(下) D(右)

### 点击命令
```
x,y坐标
```
- `540,960` - 点击坐标(540, 960)
- `100,200 800,600` - 连续点击两个位置

### 滑动命令
```
SWIPE:x1,y1,x2,y2,duration
```
- `SWIPE:800,500,800,300,500` - 从(800,500)滑动到(800,300)，持续500ms
- 主要用于游戏视角转动，推荐使用屏幕右侧区域
- duration影响滑动速度：值越小滑动越快

### 间隔时间参数
```
数字 + ms后缀
```
- `500ms` - 等待500毫秒
- `1000ms` - 等待1000毫秒（1秒）
- `2000ms` - 等待2000毫秒（2秒）
- 间隔时间参数应用到前一个命令之后
- 如果不指定，使用默认的800ms间隔

### 混合命令示例
- `W3 540,960` - 向上3次然后点击中心（默认间隔）
- `W3 1000ms 540,960` - 向上3次→等待1000ms→点击中心
- `A2 500ms SWIPE:800,600,800,300,400 D1` - 向左2次→等待500ms→滑动→向右1次
- `2160,980 500ms D3 500ms 1990,600 2000ms 2000,860` - 复杂的定时序列操作

## 配置参数

### 时间间隔参数
脚本中有几个重要的时间间隔参数，可以根据需要调整：

```bash
# 时间间隔参数 (单位: 秒)
DEFAULT_INTERVAL=0.8    # 命令之间的默认间隔 (800ms)
KEY_INTERVAL=0.8        # 按键之间的间隔 (800ms)
SEQ_INTERVAL=2.0        # 命令序列之间的间隔 (2000ms)
```

#### 参数使用场景

**DEFAULT_INTERVAL (0.8秒)**
- **用途**: 不同命令之间的间隔
- **使用场景**:
  - 点击命令后的等待
  - 滑动命令后的等待
  - 移动命令后的等待

**KEY_INTERVAL (0.8秒)**
- **用途**: 同一移动命令中多次按键之间的间隔
- **使用场景**:
  - W3 命令中，3次W键按压之间的间隔
  - A2 命令中，2次A键按压之间的间隔

**SEQ_INTERVAL (2.0秒)**
- **用途**: 不同命令序列之间的间隔
- **使用场景**:
  - auto_game.sh 中执行完一行命令后，开始下一行命令前的等待
  - 给用户足够时间观察游戏状态变化

### 配置文件格式

配置文件 `config_commands.txt` 的格式如下：

1. 每行一个命令序列，清晰易维护
2. 以`#`开头的行会被视为注释
3. 空行会被自动忽略

例如：
```
# 农场第六列
2160,980 3000ms D3 1990,600 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 农场第五列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1980,510,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms
```

### 手机端使用方法

```bash
# 查看配置
sh auto_game.sh -c

# 执行所有命令行（默认行为）
sh auto_game.sh

# 执行农场第六列
sh auto_game.sh 1

# 执行农场第五列
sh auto_game.sh 2

# 执行农场第四列
sh auto_game.sh 3

# 执行农场第三列
sh auto_game.sh 4

# 执行农场第二列
sh auto_game.sh 5

# 执行农场第一列
sh auto_game.sh 6

# 执行简单移动测试
sh auto_game.sh 7

# 执行点击测试
sh auto_game.sh 8

# 执行滑动测试
sh auto_game.sh 9

# 显示帮助
sh auto_game.sh -h
```

### 配置文件管理

```bash
# 编辑配置文件添加新的命令序列
vi config_commands.txt

# 或使用其他编辑器
nano config_commands.txt

# 在文件末尾添加新的命令序列
echo "W5 A3 540,960 D2" >> config_commands.txt

# 查看当前配置
sh auto_game.sh -c
```

## 文件说明

### 核心文件
- `move_debugger.py` - PC端主脚本，包含所有调试和测试功能
- `auto_game.sh` - 手机端执行脚本，支持配置文件管理
- `config_commands.txt` - 命令配置文件，存储预设的操作序列
- `README.md` - 完整的使用说明文档

### 生成的文件
- `move_debugger.log` - 详细的操作日志（PC端）
- `movement_calibration.txt` - 移动距离校准数据（PC端）

### 执行流程说明

#### 执行所有命令行（默认）
当执行 `sh auto_game.sh` 时：

1. 读取配置文件所有有效命令行（跳过注释和空行）
2. 依次执行每一行：
   - 执行农场第六列 → 等待2秒
   - 执行农场第五列 → 等待2秒
   - 执行农场第四列 → 等待2秒
   - 执行农场第三列 → 等待2秒
   - 执行农场第二列 → 等待2秒
   - 执行农场第一列 → 等待2秒
   - 执行简单移动测试 → 等待2秒
   - 执行点击测试 → 等待2秒
   - 执行滑动测试 → 完成

#### 执行单行命令
当执行 `sh auto_game.sh 1` 时：

1. 读取配置文件第1行有效命令（跳过注释和空行）
2. 解析该行的所有命令
3. 依次执行每个命令：
   - 点击 (2160,980)
   - 等待 3000ms
   - 向右移动 3 次
   - 点击 (1990,600)
   - ...以此类推

#### 当前配置文件包含
- 第1行：农场第六列
- 第2行：农场第五列
- 第3行：农场第四列
- 第4行：农场第三列
- 第5行：农场第二列
- 第6行：农场第一列
- 第7行：简单移动测试
- 第8行：点击测试
- 第9行：滑动测试

## 技术原理

### 为什么选择keyevent而不是swipe？
1. **精确性**: keyevent是离散事件，移动距离更可控
2. **稳定性**: 不受滑动时间和速度影响
3. **重现性**: 相同的按键次数产生相同的移动效果

### 为什么使用--longpress？
经过测试发现，普通的keyevent无法让游戏角色移动，只有longpress方法才能被游戏识别为有效的按键输入。

### 参数一致性保证

#### 键位映射
**auto_game.sh** 和 **move_debugger.py** 中的键位映射完全一致：

```
W键: keycode=51  # 向上移动
A键: keycode=29  # 向左移动
S键: keycode=47  # 向下移动
D键: keycode=32  # 向右移动
```

#### 按键执行方法
两个文件都使用相同的按键执行方法：

**auto_game.sh**:
```bash
input keyevent --longpress $keycode
```

**move_debugger.py**:
```python
adb shell input keyevent --longpress {keycode}
```

#### 参数验证方法
可以通过以下方式验证参数一致性：

1. **运行 move_debugger.py**:
   ```bash
   python move_debugger.py
   ```
   选择移动测试，观察间隔时间是否为0.8秒

2. **运行 auto_game.sh**:
   ```bash
   sh auto_game.sh 7  # 执行简单移动测试
   ```
   观察移动命令的执行间隔

3. **对比日志输出**:
   两个程序的日志应该显示相同的时间间隔值

## 故障排除

### PC端常见问题
1. **角色不移动**
   - 确认使用的是longpress方法
   - 检查游戏是否处于可移动状态
   - 验证按键映射是否正确

2. **ADB连接失败**
   - 检查USB调试是否开启
   - 确认ADB驱动是否正确安装
   - 尝试重新连接设备

3. **按键无响应**
   - 确认游戏窗口处于焦点状态
   - 检查设备是否锁屏
   - 验证keycode是否正确

### 手机端常见问题

1. **权限被拒绝**
   ```bash
   # 解决方案: 确保有root权限
   su
   # 然后再执行脚本
   ```

2. **命令不存在**
   ```bash
   # 检查input命令是否可用
   which input
   # 或者
   input --help
   ```

3. **脚本无法执行**
   ```bash
   # 检查文件权限
   ls -l *.sh
   # 设置执行权限
   chmod +x *.sh
   ```

4. **游戏无响应**
   - 确认游戏处于前台运行状态
   - 检查坐标是否正确
   - 验证按键映射是否有效

### 调试技巧

#### PC端调试
- 查看 `move_debugger.log` 获取详细错误信息
- 使用单次按键测试验证每个方向
- 逐步增加移动次数，观察效果变化

#### 手机端调试

1. **测试单个命令**
   ```bash
   # 测试点击
   input tap 540 960

   # 测试按键
   input keyevent --longpress 51

   # 测试滑动
   input swipe 800 500 800 300 500
   ```

2. **查看日志输出**
   - 脚本会输出详细的执行日志
   - 观察每个步骤的执行情况

3. **分段测试**
   - 将复杂序列分解为小段测试
   - 逐步验证每个操作的效果

### 权限要求
- **PC端**: 需要ADB连接权限和USB调试权限
- **手机端**: 必须有root权限才能执行input命令
- 确保终端应用有root访问权限

### 游戏兼容性
- 脚本使用Android标准input命令
- 兼容大部分Android游戏
- 某些游戏可能有反作弊检测

### 坐标适配
- 脚本中的坐标基于特定屏幕分辨率
- 不同设备可能需要调整坐标值
- 建议先测试单个操作确认坐标准确性

## 推荐终端应用

### Termux (推荐)
- **优点**: 功能强大，支持包管理
- **下载**: Google Play Store 或 F-Droid
- **特点**: 无需root即可使用基本功能

### Terminal Emulator
- **优点**: 轻量级，启动快速
- **适用**: 简单脚本执行
- **特点**: 占用空间小

### ADB Shell
- **优点**: 通过USB连接使用
- **适用**: 开发调试环境
- **特点**: 无需在手机上安装额外应用

## 扩展功能

### 自动化脚本开发
基于校准数据，可以开发更复杂的自动化功能：
- 自动收菜路径规划
- 定时任务执行
- 多点位巡逻
- 资源采集优化

### 虚拟手柄
未来可以开发图形界面，提供：
- 屏幕上的WASD控制按钮
- 实时移动距离显示
- 路径录制和回放功能

## 注意事项

### 使用建议
1. **首次使用前**
   - 先在测试环境验证脚本效果
   - 确认所有坐标和操作的准确性

2. **使用过程中**
   - 保持游戏处于前台状态
   - 避免在脚本执行时手动操作

3. **安全使用**
   - 遵守游戏服务条款
   - 合理控制自动化频率
   - 定期备份游戏数据

### 参数修改注意事项
1. **参数修改**: 如需调整时间间隔，请同时修改两个文件中的对应参数
2. **测试验证**: 修改参数后，建议先用 move_debugger.py 测试效果，再用 auto_game.sh 执行实际任务
3. **备份配置**: 重要参数修改前建议备份原始配置

### 技术支持
如果遇到问题，请检查：
1. 设备是否已正确root（手机端）
2. ADB连接是否正常（PC端）
3. 终端应用是否有root权限（手机端）
4. 脚本文件是否有执行权限
5. 游戏是否处于可操作状态
6. 坐标是否适配当前设备分辨率

---

**项目维护**: 两个文件的参数已完全一致，确保了行为的统一性！
