# 元梦之星农场自动化脚本配置文件
# 每行一个命令序列，清晰易维护
# 支持注释（以#开头的行会被忽略）

# 农场第六列
2160,980 3000ms D3 1990,600 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 农场第五列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1980,510,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 农场第四列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1960,520,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 农场第三列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1940,530,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 农场第二列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1920,540,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 农场第一列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1900,550,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 简单移动测试
W2 A2 S2 D2

# 点击测试
540,960 1000ms 1080,540 1000ms 540,1920

# 滑动测试
SWIPE:800,600,800,300,500 1000ms SWIPE:800,300,800,600,500
